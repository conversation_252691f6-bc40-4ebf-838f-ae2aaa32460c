import 'package:get/get.dart';

class BindingCodeController extends GetxController {
  var deviceType = ''.obs;
  var bindingCode = ''.obs;
  var deviceTypeError = ''.obs;
  var bindingCodeError = ''.obs;
  var isLoading = false.obs;

  final deviceTypes = ["Tablet", "Phone", "PC", "Smart Board"];

  void setDeviceType(String value) {
    deviceType.value = value;
  }

  void setBindingCode(String value) {
    bindingCode.value = value.toUpperCase();
  }

  Future<void> submit() async {
    // Clear previous errors
    deviceTypeError.value = '';
    bindingCodeError.value = '';
    
    bool isValid = true;
    
    if (deviceType.isEmpty) {
      deviceTypeError.value = 'Please select a device type';
      isValid = false;
    }
    
    if (bindingCode.value.length != 8) {
      bindingCodeError.value = 'Binding code must be exactly 8 characters';
      isValid = false;
    }
    
    if (!isValid) return;

    isLoading.value = true;
    
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Navigate to confirmation page with the binding details
      Get.toNamed(
        '/binding/confirmation',
        arguments: {
          'schoolName': 'Government Secondary School, New Haven', // Replace with actual school name from API
          'schoolLocation': 'New Haven, Enugu East LGA', // Replace with actual location from API
          'bindingCode': bindingCode.value,
          'deviceType': deviceType.value,
          'status': 'Pending',
        },
      );
    } catch (e) {
      Get.snackbar("Error", "Failed to process binding. Please try again.");
    } finally {
      isLoading.value = false;
    }
  }
}
