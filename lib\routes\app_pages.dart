import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../modules/auth/controllers/auth_controller.dart';
import '../modules/dashboard/bindings/dashboard_binding.dart';
import '../modules/dashboard/pages/dashboard_page.dart';
import '../modules/onboarding/bindings/onboarding_binding.dart';
import '../modules/onboarding/pages/binding_code_page.dart';
import '../modules/onboarding/pages/confirmation_page.dart';
import '../modules/onboarding/pages/splash_page.dart';
import '../modules/onboarding/pages/success_page.dart';

class AuthGuard extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    try {
      final auth = Get.find<AuthController>();
      if (!auth.isAuthenticated) {
        return const RouteSettings(name: '/login');
      }
    } catch (_) {
      return const RouteSettings(name: '/login');
    }
    return null;
  }
}

final routes = [
  GetPage(name: '/', page: () => SplashPage(), binding: OnboardingBinding()),
  GetPage(
    name: '/binding',
    page: () => BindingCodePage(),
    binding: OnboardingBinding(),
  ),
  GetPage(
    name: '/binding/confirmation',
    page: () => ConfirmationPage.fromRoute(),
    binding: OnboardingBinding(),
  ),
  GetPage(
    name: '/binding/success',
    page: () => SuccessPage(),
    binding: OnboardingBinding(),
  ),
  GetPage(
    name: '/dashboard',
    page: () => DashboardPage(),
    binding: DashboardBinding(),
  ),
];
