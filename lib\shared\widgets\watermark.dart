import 'package:flutter/material.dart';

class Watermark extends StatelessWidget {
  const Watermark({super.key});

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: IgnorePointer(
        ignoring: true, // no touch events
        child: Opacity(
          opacity: 0.08,
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Define approximate cell size
              const double cellSize = 120;

              // Calculate columns & rows based on available width/height
              final int columns = (constraints.maxWidth / cellSize).ceil();
              final int rows = (constraints.maxHeight / cellSize).ceil();
              final int totalItems = columns * rows;

              return GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: columns,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: totalItems,
                itemBuilder: (context, index) {
                  return Center(
                    child: Transform.rotate(
                      angle: 0.785398, // 45 degrees in radians
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: const [
                          Text(
                            "ENUGU STATE",
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          Text(
                            "GOVERNMENT",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
