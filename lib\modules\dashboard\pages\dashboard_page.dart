import 'package:flutter/material.dart';

import '../../../shared/widgets/app_bar_widget.dart';
import '../widgets/activity_item.dart';
import '../widgets/alert_item.dart';
import '../widgets/scrollable_section.dart';
import '../widgets/status_card.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width > 800;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBarWidget(title: "System Dashboard"),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Align(
          alignment: Alignment.topCenter,
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 1200),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  "System Status Overview",
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),

                const SizedBox(height: 16),

                // ✅ Responsive Status Grid
                GridView.count(
                  crossAxisCount: isWide ? 4 : 2,
                  crossAxisSpacing: 10,
                  mainAxisSpacing: 8,
                  childAspectRatio: isWide
                      ? 1.2
                      : 1.0, // Adjust aspect ratio for fixed height
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    StatusCard(
                      icon: Icons.wifi,
                      title: "Network Status",
                      status: "Pending",
                      value: "Connected",
                      subtitle: "Signal Strength: 98%",
                    ),
                    StatusCard(
                      icon: Icons.health_and_safety,
                      title: "System Health",
                      value: "Optimal",
                      subtitle: "All systems operational",
                    ),
                    StatusCard(
                      icon: Icons.timer,
                      title: "Monitoring Uptime",
                      value: "99.8%",
                      subtitle: "Device monitoring availability",
                    ),
                    StatusCard(
                      icon: Icons.speed,
                      title: "Performance",
                      value: "95%",
                      subtitle: "System performance score",
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Activity and Alerts Section
                LayoutBuilder(
                  builder: (context, constraints) {
                    final isSmall = constraints.maxWidth < 500;

                    // Create activity items
                    final activityItems = [
                      ActivityItem(
                        icon: Icons.check_circle,
                        text: "System startup completed",
                        time: "5 minutes ago",
                        color: Colors.green,
                      ),
                      ActivityItem(
                        icon: Icons.people,
                        text: "New monitoring session started",
                        time: "15 minutes ago",
                        color: Colors.blue,
                      ),
                      ActivityItem(
                        icon: Icons.sync,
                        text: "Configuration sync completed",
                        time: "1 hour ago",
                        color: Colors.grey,
                      ),
                      ActivityItem(
                        icon: Icons.sync,
                        text: "Configuration sync completed",
                        time: "1 hour ago",
                        color: Colors.grey,
                      ),
                      ActivityItem(
                        icon: Icons.sync,
                        text: "Configuration sync completed",
                        time: "1 hour ago",
                        color: Colors.grey,
                      ),
                      ActivityItem(
                        icon: Icons.sync,
                        text: "Configuration sync completed",
                        time: "1 hour ago",
                        color: Colors.grey,
                      ),
                      ActivityItem(
                        icon: Icons.sync,
                        text: "Configuration sync completed",
                        time: "1 hour ago",
                        color: Colors.grey,
                      ),
                      ActivityItem(
                        icon: Icons.sync,
                        text: "Configuration sync completed",
                        time: "1 hour ago",
                        color: Colors.grey,
                      ),
                      ActivityItem(
                        icon: Icons.sync,
                        text: "Configuration sync completed",
                        time: "1 hour ago",
                        color: Colors.grey,
                      ),
                    ];

                    // Create alert items
                    final alertItems = [
                      AlertItem(
                        icon: Icons.warning_amber_rounded,
                        title: "Software Update Available",
                        subtitle:
                            "New monitoring features are available for installation",
                        time: "2 hours ago",
                        color: Colors.orange,
                      ),
                      AlertItem(
                        icon: Icons.schedule,
                        title: "Scheduled Maintenance",
                        subtitle: "System maintenance planned for this weekend",
                        time: "1 day ago",
                        color: Colors.blue,
                      ),
                      AlertItem(
                        icon: Icons.schedule,
                        title: "Scheduled Maintenance",
                        subtitle: "System maintenance planned for this weekend",
                        time: "1 day ago",
                        color: Colors.blue,
                      ),
                      AlertItem(
                        icon: Icons.schedule,
                        title: "Scheduled Maintenance",
                        subtitle: "System maintenance planned for this weekend",
                        time: "1 day ago",
                        color: Colors.blue,
                      ),
                      AlertItem(
                        icon: Icons.schedule,
                        title: "Scheduled Maintenance",
                        subtitle: "System maintenance planned for this weekend",
                        time: "1 day ago",
                        color: Colors.blue,
                      ),
                      AlertItem(
                        icon: Icons.schedule,
                        title: "Scheduled Maintenance",
                        subtitle: "System maintenance planned for this weekend",
                        time: "1 day ago",
                        color: Colors.blue,
                      ),
                    ];

                    if (isSmall) {
                      return Column(
                        children: [
                          ScrollableSection(
                            title: "Recent Activity",
                            items: activityItems,
                            maxHeight: 300,
                          ),
                          const SizedBox(height: 16),
                          ScrollableSection(
                            title: "System Alerts",
                            items: alertItems,
                            maxHeight: 300,
                          ),
                        ],
                      );
                    }

                    return Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: ScrollableSection(
                            title: "Recent Activity",
                            items: activityItems,
                            maxHeight: 400,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ScrollableSection(
                            title: "System Alerts",
                            items: alertItems,
                            maxHeight: 400,
                          ),
                        ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 24),

                // ✅ Footer
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.shade50,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: const [
                      Text(
                        "Smart School Monitor",
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Spacer(),
                      Chip(
                        label: Text("Version 2.1.0"),
                        backgroundColor: Colors.white,
                      ),
                      SizedBox(width: 8),
                      Chip(
                        label: Text("Connected"),
                        backgroundColor: Colors.green,
                        labelStyle: TextStyle(color: Colors.white),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
